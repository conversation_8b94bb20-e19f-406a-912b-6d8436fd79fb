2025-07-11 20:46:31,945 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-11 20:46:31,946 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-11 20:46:31,946 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-11 20:46:31,946 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-11 20:46:31,947 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-11 20:46:31,947 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-11 20:46:31,948 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-11 20:46:31,948 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-11 20:46:31,948 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-11 20:46:31,949 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-11 20:46:31,949 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-11 20:46:31,949 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-11 20:46:31,950 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-11 20:46:34,052 - __main__ - INFO - Existing processes terminated
2025-07-11 20:46:35,455 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-07-11 20:46:35,456 - utils.global_values_db - INFO - Global values database initialized successfully
2025-07-11 20:46:35,456 - utils.global_values_db - INFO - Using global values from config.py
2025-07-11 20:46:35,456 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-11 20:46:35,458 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-11 20:46:35,458 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-11 20:46:35,505 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-11 20:46:35,987 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-11 20:46:35,988 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-11 20:46:35,988 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-11 20:46:35,989 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-11 20:46:35,989 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-11 20:46:35,990 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-11 20:46:35,990 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-11 20:46:35,990 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-11 20:46:35,990 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-11 20:46:35,990 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-11 20:46:35,991 - utils.database - INFO - Database initialized successfully
2025-07-11 20:46:35,991 - utils.database - INFO - Checking initial database state...
2025-07-11 20:46:36,028 - utils.database - INFO - Database state: 0 suites, 0 cases, 11198 steps, 1 screenshots, 2 tracking entries
2025-07-11 20:46:36,048 - app - INFO - Using directories from config.py:
2025-07-11 20:46:36,048 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-11 20:46:36,048 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-11 20:46:36,048 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-11 20:46:36,183] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-11 20:46:36,197] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x116f32a50>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-11 20:46:36,197] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-07-11 20:46:36,243] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-07-11 20:46:36,291] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
